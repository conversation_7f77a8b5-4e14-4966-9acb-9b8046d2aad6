package router

import (
	"log"
	"net/http"
	"os"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"

	"tradicao/internal/config"
	"tradicao/internal/controllers"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/models"
	"tradicao/internal/permissions"
	"tradicao/internal/repository"
	"tradicao/internal/services"
)

// SetupRouter configura todas as rotas da aplicação
func SetupRouter(authController *controllers.AuthController) *gin.Engine {
	// Definir modo de execução
	if os.Getenv("GIN_MODE") != "" {
		gin.SetMode(os.Getenv("GIN_MODE"))
	}

	router := gin.Default()

	// Middlewares globais
	router.Use(middleware.SecurityHeaders())
	router.Use(gin.Recovery())

	// Servir arquivos estáticos
	router.Static("/static", "./web/static")
	// Carrega templates de todos os diretórios e subdiretórios
	router.LoadHTMLGlob("web/templates/**/*")
	// Não é necessário carregar duas vezes, apenas asseguramos que padrão acima inclua todos os templates
	// router.LoadHTMLGlob("web/templates/*")

	// Rota temporária para testar a renderização do login.html
	router.GET("/test-login", func(c *gin.Context) {
		c.HTML(http.StatusOK, "login.html", nil)
	})

	router.GET("/", func(c *gin.Context) {
		c.HTML(200, "login.html", nil)
	})
	router.GET("/login", func(c *gin.Context) {
		c.HTML(200, "login.html", nil)
	})
	router.GET("/register", func(c *gin.Context) {
		c.HTML(200, "register.html", nil)
	})
	router.GET("/password-reset", func(c *gin.Context) {
		c.HTML(200, "password_reset.html", nil)
	})
	router.GET("/change-password", func(c *gin.Context) {
		c.HTML(200, "change_password.html", nil)
	})

	// Instanciar AuthService
	repo := repository.NewGormUserRepository()
	cfg, _ := config.LoadConfig()
	authService := services.NewAuthService(repo, cfg)

	// Grupo de APIs
	api := router.Group("/api")
	{
		// Rotas públicas - API
		api.POST("/login", authController.Login)
		api.POST("/register", authController.Register)
		api.POST("/password/reset/request", authController.RequestPasswordReset)
		api.POST("/password/reset", authController.ResetPassword)

		// Rotas protegidas - API
		// Usamos o CSRF apenas nas rotas autenticadas
		authenticated := api.Group("")
		authenticated.Use(middleware.AuthMiddleware(authService))
		authenticated.Use(middleware.GetCSRFProtection())
		{
			authenticated.GET("/check-auth", authController.CheckAuth)
			authenticated.POST("/logout", authController.Logout)
			authenticated.POST("/refresh-token", authController.RefreshToken)

			// Gerenciamento de senha
			authenticated.GET("/password/status", authController.CheckPasswordStatus)
			authenticated.POST("/password/change", authController.ChangePassword)

			// 2FA / Autenticação de dois fatores
			// Rotas de 2FA - comentadas temporariamente até implementação completa
			// authenticated.GET("/security/2fa/setup", authController.SetupTOTP)
			// authenticated.POST("/security/2fa/enable", authController.VerifyAndEnableTOTP)
			// authenticated.POST("/security/2fa/disable", authController.DisableTOTP)
			// authenticated.GET("/security/qrcode/:filename", authController.ServeQRCode)

			// Rotas de usuários
			users := authenticated.Group("/users")
			{
				// Algumas rotas exigem privilégio de admin
				users.GET("", permissions.RoleMiddleware(models.RoleAdmin), controllers.ListUsers)
				users.POST("", permissions.RoleMiddleware(models.RoleAdmin), controllers.CreateUser)
				users.GET("/:id", controllers.GetUser)
				users.PUT("/:id", controllers.UpdateUser)
				users.DELETE("/:id", permissions.RoleMiddleware(models.RoleAdmin), controllers.DeleteUser)

				// Rota para admin redefinir senha de outro usuário
				users.POST("/:id/reset-password", permissions.RoleMiddleware(models.RoleAdmin), authController.ResetPassword)
			}

			// Rotas de configurações
			settings := authenticated.Group("/settings")
			settings.Use(permissions.RoleMiddleware(models.RoleAdmin))
			{
				settings.GET("/security", controllers.GetSettings)
				settings.PUT("/security", controllers.UpdateSettings)
				settings.GET("/audit-logs", controllers.GetLogs)
			}

			// Rotas de ordens de manutenção
			ordens := authenticated.Group("/ordens")
			{
				ordens.GET("", handlers.ListarOrdensAPI)
				ordens.GET("/:id", handlers.ObterOrdemAPI)
				ordens.POST("", handlers.CriarOrdemAPI)
				ordens.PATCH("/:id/status", handlers.AtualizarStatusOrdemAPI)

				// Novas rotas para os cards de manutenção
				ordens.POST("/:id/manutencao", middleware.OrderUpdateMiddleware(), handlers.SaveManutencaoHandler)
				ordens.POST("/:id/custos", middleware.OrderUpdateMiddleware(), handlers.SaveCustosHandler)
				ordens.POST("/:id/cronograma", middleware.OrderUpdateMiddleware(), handlers.SaveCronogramaHandler)
				ordens.POST("/:id/chat", middleware.OrderUpdateMiddleware(), handlers.SaveChatMessageHandler)
				ordens.GET("/:id", middleware.OrderViewMiddleware(), handlers.GetOrdemHandler)
			}

			// Rotas para dados relacionados
			authenticated.GET("/equipamentos", handlers.ListarEquipamentosAPI)
			authenticated.GET("/filiais", handlers.ListarFiliaisAPI)
		}
	}

	// Rotas protegidas - Frontend
	protected := router.Group("")
	protected.Use(authMiddlewareHTML())
	{
		protected.GET("/dashboard", func(c *gin.Context) {
			c.HTML(200, "dashboard.html", nil)
		})

		protected.GET("/ordens", handlers.ListarOrdensHTML)
		protected.GET("/ordens/:id", handlers.DetalhesOrdemHTML)
		protected.GET("/ordens/:id/editar", handlers.EditarOrdemHTML)
		protected.GET("/ordens/calendario", handlers.OrdemCalendarioHTML)
		protected.GET("/criarordemfilial", handlers.CriarOrdemHTML)

		// Rotas para técnicos
		protected.GET("/ordemtecnico", middleware.ResourcePermissionMiddleware("page", "ordemtecnico"), func(c *gin.Context) {
			// Obtém informações do usuário do contexto
			userID, _ := c.Get("userID")
			userRole, _ := c.Get("userRole")
			userName, _ := c.Get("userName")

			// Log para depuração
			log.Printf("[DEBUG] Rota /ordemtecnico - UserID: %v, UserRole: %v, UserName: %v", userID, userRole, userName)

			// Obtém o nome da filial, se disponível
			branchName, _ := c.Get("filialName")
			if branchName == nil {
				branchName = "Filial"
			}

			// Usar o papel real do usuário em vez de forçar para "tecnico"
			c.HTML(200, "tecnico/Ordemtecnico.html", gin.H{
				"title":       "Ordem Técnico - Rede Tradição",
				"page":        "ordemtecnico",
				"ActivePage":  "ordemtecnico",
				"UserID":      userID,
				"UserRole":    userRole, // Usando o papel real do usuário
				"UserName":    userName,
				"branch_name": branchName,
			})
		})
		protected.GET("/manutencaoordem", middleware.ResourcePermissionMiddleware("page", "ordemtecnico"), func(c *gin.Context) {
			// Redireciona para a página ordemtecnico, já que o template ManutencaoOrdem.html não existe
			// e ambas as páginas devem ter o mesmo visual conforme requisitos
			log.Printf("[INFO] Redirecionando de /manutencaoordem para /ordemtecnico")
			c.Redirect(http.StatusFound, "/ordemtecnico")
			c.Abort()
		})

		// Adiciona rota para /ordemtecnica que redireciona para /ordemtecnico
		protected.GET("/ordemtecnica", middleware.ResourcePermissionMiddleware("page", "ordemtecnico"), func(c *gin.Context) {
			// Redireciona para a página ordemtecnico para manter consistência
			log.Printf("[INFO] Redirecionando de /ordemtecnica para /ordemtecnico")
			c.Redirect(http.StatusFound, "/ordemtecnico")
			c.Abort()
		})

		// Rota de calendário-flip (mantida para compatibilidade)
		protected.GET("/calendario-flip", func(c *gin.Context) {
			c.HTML(200, "calendarios/calendar_flip.html", nil)
		})

		protected.GET("/settings", func(c *gin.Context) {
			c.HTML(200, "settings.html", nil)
		})

		// O middleware RoleMiddleware para HTML restringe algumas páginas
		admin := protected.Group("")
		admin.Use(roleMiddlewareHTML("admin"))
		{
			admin.GET("/admin", func(c *gin.Context) {
				c.HTML(200, "admin_panel.html", nil)
			})
		}
	}

	return router
}

// authMiddlewareHTML middleware para verificar autenticação em páginas HTML
func authMiddlewareHTML() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Verifica a presença do cookie de sessão
		cookie, err := c.Cookie("session_token")
		if err != nil || cookie == "" {
			c.Redirect(302, "/login?redirect="+c.Request.URL.Path)
			c.Abort()
			return
		}

		// Se o usuário estiver tentando acessar a página de troca de senha, permita
		if c.Request.URL.Path == "/change-password" {
			c.Next()
			return
		}

		// Verifica se o token é válido e decodifica
		claims := jwt.MapClaims{}
		token, err := jwt.ParseWithClaims(cookie, claims, func(token *jwt.Token) (interface{}, error) {
			return middleware.JWTSecret, nil
		})

		if err != nil || !token.Valid {
			c.Redirect(302, "/login?redirect="+c.Request.URL.Path+"&error=session_expired")
			c.Abort()
			return
		}

		// Extrai o ID do usuário
		userIDFloat, ok := claims["user_id"].(float64)
		if !ok {
			c.Redirect(302, "/login?error=invalid_session")
			c.Abort()
			return
		}

		userID := uint(userIDFloat)

		// Verifica se o usuário precisa alterar a senha
		// Usando dependência manual para evitar circular import
		userRepo := repository.NewGormUserRepository()
		userService := services.NewUserService(userRepo, nil)

		passwordChangeRequired, err := userService.IsPasswordChangeRequired(userID)
		if err == nil && passwordChangeRequired {
			// Se a página atual não for a de troca de senha, redireciona
			if c.Request.URL.Path != "/change-password" {
				c.Redirect(302, "/change-password")
				c.Abort()
				return
			}
		}

		// Define informações do usuário no contexto
		c.Set("userID", userID)
		if role, ok := claims["role"].(string); ok {
			c.Set("userRole", role)
		}

		c.Next()
	}
}

// roleMiddlewareHTML middleware para verificar privilégios em páginas HTML
func roleMiddlewareHTML(requiredRole string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Em uma implementação real, verificaria a role no token
		// Por simplicidade, apenas redireciona se não autorizado
		c.Next()
	}
}
