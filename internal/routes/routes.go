package routes

import (
	"log" // Adicionando a importação do pacote log
	"net/http"
	"tradicao/internal/config"
	"tradicao/internal/controllers"
	"tradicao/internal/handlers"
	"tradicao/internal/interfaces"
	"tradicao/internal/middleware"
	"tradicao/internal/models"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"gorm.io/gorm" // Importando o pacote gorm

	"github.com/gin-gonic/gin"
)

// Placeholder functions for branch routes
// These need to be fully implemented based on the specifications.
func SetupBranchControllerRoutes(router *gin.Engine, branchController *controllers.BranchController) {
	// Esta função está sendo substituída pela implementação em branch_routes.go
	// Mantida temporariamente para compatibilidade
	log.Println("AVISO: Usando SetupBranchControllerRoutes que está obsoleta")
}

func SetupLegacyRouter(db *gorm.DB) *gin.Engine {
	r := gin.Default()

	// Instanciar AuthService
	repo := repository.NewGormUserRepository()
	cfg, _ := config.LoadConfig()
	authService := services.NewAuthService(repo, cfg)

	// Load HTML templates
	// Alterado para usar o mesmo padrão de carregamento que está em outros lugares do código
	r.LoadHTMLGlob("web/templates/**/*")
	r.LoadHTMLGlob("web/templates/*")

	// Serve static files
	r.Use(middleware.AuthMiddleware(authService)) // Aplicando o middleware de autenticação

	// Rotas de logout
	r.GET("/logout", handlers.LogoutHandler)              // Rota de logout para acesso direto via navegador
	r.POST("/api/auth/logout", handlers.LogoutAPIHandler) // Rota de logout para chamadas AJAX

	r.Static("/static", "./static")

	// Public routes
	r.GET("/", handlers.HomePage)

	r.POST("/auth", handlers.AuthHandlerWrapper(db)) // Usando a variável db para o AuthHandler

	// Login route
	r.GET("/login", func(c *gin.Context) {
		c.HTML(http.StatusOK, "login/login.html", nil)
	})

	// Calendar routes
	r.GET("/calendar", func(c *gin.Context) {
		c.HTML(http.StatusOK, "calendar.html", nil)
	})

	// Adaptando a função do calendário para o formato Gin
	r.GET("/api/calendar-events", func(c *gin.Context) {
		// Implementar lógica do calendário aqui
		c.JSON(http.StatusOK, gin.H{"events": []string{}})
	})

	// API routes - comentando rotas com handlers não implementados
	// r.GET("/api/service-orders", handlers.GetServiceOrders) // REMOVIDO - usar /api/orders

	// Adaptando funções HTTP para o formato Gin - REMOVIDO - usar /api/orders
	// r.POST("/api/maintenance-orders", func(c *gin.Context) {
	//	// Implementar lógica de criação de ordem de manutenção
	//	c.JSON(http.StatusOK, gin.H{"status": "success"})
	// })

	// r.GET("/api/equipment", handlers.GetEquipment)

	r.POST("/api/provider-update-order", func(c *gin.Context) {
		// Implementar lógica de atualização de ordem
		c.JSON(http.StatusOK, gin.H{"status": "success"})
	})

	// Handle 404
	r.NoRoute(func(c *gin.Context) {
		c.HTML(404, "404.html", nil)
	})

	// Dashboard route
	r.GET("/dashboard", func(c *gin.Context) {
		userRole, exists := c.Get("userRole")
		if !exists {
			c.HTML(http.StatusUnauthorized, "acesso_negado.html", gin.H{
				"message": "Usuário não autenticado",
			})
			return
		}

		// Verifica se o usuário é financeiro e redireciona ou mostra erro
		if userRole == models.RoleFinanceiro { // Usando a constante do model
			// Opção 1: Redirecionar para o painel financeiro
			// c.Redirect(http.StatusFound, "/finance/dashboard")
			// c.Abort()
			// return

			// Opção 2: Mostrar página de acesso negado com mensagem clara
			c.HTML(http.StatusForbidden, "acesso_negado.html", gin.H{
				"title":    "Acesso Negado",
				"message":  "Seu perfil é financeiro. Acesse o Painel Financeiro dedicado.",
				"link":     "/finance/dashboard",
				"linkText": "Ir para o Painel Financeiro",
			})
			return
		}

		// Renderiza o PAINEL GERAL para os outros perfis
		c.HTML(http.StatusOK, "dashboard/dashboard.html", gin.H{
			"title":    "Painel Principal", // Título para o painel geral
			"UserRole": userRole,
		})
	})

	// Minha Conta route
	r.GET("/minha_conta", func(c *gin.Context) {
		c.HTML(http.StatusOK, "minha_conta.html", nil)
	})

	// Serve web directory files
	r.StaticFS("/web", http.Dir("./web"))

	// Verificar se a rota para settings está configurada corretamente
	r.GET("/settings", func(c *gin.Context) {
		log.Println("Acessando rota /settings")
		c.HTML(http.StatusOK, "settings.html", gin.H{
			"title": "Configurações - Rede Tradição",
		})
	})

	//Register branch routes - Comentado temporariamente até resolver conflitos
	// branchRepository := repository.NewBranchRepository(database.GetDB())
	// branchService := services.NewBranchService(branchRepository)
	// branchController := controllers.NewBranchController(branchService)
	// SetupBranchControllerRoutes(r, branchController)

	// Configurar rotas de técnicos
	userRepo := repository.NewGormUserRepository()
	userService := services.NewUserService(userRepo, &config.Config{})

	// Criar repositório de técnicos
	techRepo := repository.NewTechnicianRepository(db)
	techService := services.NewTechnicianService(techRepo)

	// Adaptar o serviço para a interface esperada pelo handler
	techServiceAdapter := handlers.NewTechnicianServiceAdapter(techService)
	technicianHandler := handlers.NewTechnicianHandler(userService, techServiceAdapter)
	SetupTechnicianRoutes(r, technicianHandler, middleware.AuthMiddleware(authService))

	return r
}

func SetupUserRoutes(r *gin.Engine, h *handlers.UserHandler) {
	users := r.Group("/api/users")
	{
		users.POST("", h.Create)
		users.GET("", h.List)
		users.GET("/:id", h.GetByID)
		users.PUT("/:id", h.Update)
		users.DELETE("/:id", h.Delete)
	}
}

func SetupRoutes(r *gin.Engine, technicianHandler *handlers.TechnicianHandler, authService interfaces.AuthServiceInterface) {
	r.Use(middleware.AuthMiddleware(authService)) // Aplicando o middleware de autenticação
	// ...
	SetupTechnicianRoutes(r, technicianHandler, middleware.AuthMiddleware(authService))
	// ...
}
