package routes

import (
	"tradicao/internal/config"
	"tradicao/internal/controllers"
	"tradicao/internal/middleware"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

func SetupRouter() *gin.Engine {
	r := gin.Default()

	// Configurar templates
	r.LoadHTMLGlob("web/templates/**/*")

	// Configurar arquivos estáticos
	r.Static("/static", "web/static")

	// Middleware de autenticação
	repo := repository.NewGormUserRepository()
	cfg, _ := config.LoadConfig()
	authService := services.NewAuthService(repo, cfg)

	// Configurar rotas
	notificationController := controllers.NewNotificationController()
	SetupNotificationRoutes(r, notificationController, authService)
	SetupProviderAssignmentRoutes(r, middleware.AuthMiddleware(authService))
	SetupEquipmentTypeRoutes(r, middleware.AuthMiddleware(authService))
	SetupOrderAssignmentRoutes(r, middleware.AuthMiddleware(authService))
	SetupAuditLogRoutes(r, middleware.AuthMiddleware(authService))

	// Configurar sistema de gerenciamento de vínculos
	// Nota: Esta função será chamada em main.go com os parâmetros corretos
	// SetupLinkManagementSystem(r, db, technicianOrderService)

	// ... outras rotas ...

	return r
}
